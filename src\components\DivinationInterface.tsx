import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { DivinationCard } from './DivinationCard';
import { MarkdownRenderer } from './MarkdownRenderer';
import { YaoType } from './YaoLine';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Hexagram, generateHexagram } from './HexagramUtils';
import { HexagramDisplay } from './HexagramDisplay';
import { ManualCoinInput } from './ManualCoinInput';
import { AutoCoinToss } from './AutoCoinToss';
import { useAuthStore } from '@/stores/authStore';

import { useTranslation } from '@/hooks/useTranslation';
import { BookOpen } from 'lucide-react';
import { toast as sonnerToast } from 'sonner';
import { supabase } from '@/lib/supabase';
import { useNavigate } from 'react-router-dom';

interface CoinResult {
  coins: number[];
  sum: number;
  lineType: YaoType;
}

export const DivinationInterface: React.FC = () => {
  const [question, setQuestion] = useState('');
  const [currentHexagram, setCurrentHexagram] = useState<Hexagram | null>(null);

  const [isGenerating, setIsGenerating] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<string>('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showManualInput, setShowManualInput] = useState(false);
  const [showAutoToss, setShowAutoToss] = useState(false);
  const [coinResults, setCoinResults] = useState<CoinResult[]>([]);

  const [divinationTime, setDivinationTime] = useState<Date | null>(null);

  const { toast } = useToast();
  const { user } = useAuthStore();

  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleAutoGenerate = () => {
    if (!question.trim()) {
      toast({
        title: t('pleaseInputQuestion'),
        description: t('inputQuestionFirst'),
        variant: "destructive"
      });
      return;
    }

    setAnalysisResult('');
    setShowAutoToss(true);
  };

  const handleAutoComplete = (lines: YaoType[], results: CoinResult[]) => {
    const hexagram = generateHexagram(lines);
    setCurrentHexagram(hexagram);
    setCoinResults(results);
    setDivinationTime(new Date());


    setShowAutoToss(false);
    toast({
      title: t('hexagramGenerated'),
      description: t('hexagramGeneratedDesc'),
    });
  };

  const handleManualComplete = (lines: YaoType[], results: CoinResult[], customDate?: Date) => {
    const hexagram = generateHexagram(lines, customDate);
    setCurrentHexagram(hexagram);
    setCoinResults(results);
    setDivinationTime(customDate || new Date());


    setShowManualInput(false);
    toast({
      title: t('hexagramGenerated'),
      description: t('hexagramGeneratedDesc'),
    });
  };

  const handleAnalyze = async () => {
    if (!currentHexagram || !user) {
      toast({
        title: t('pleaseLogin'),
        description: t('analysisRequiresLogin'),
        variant: "destructive"
      });
      return;
    }

    if (!question.trim()) {
      toast({
        title: t('pleaseInputQuestion'),
        description: t('inputQuestionFirst'),
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);

    try {
      // 准备保存到数据库的记录数据
      console.log('🔍 检查currentHexagram对象:', currentHexagram);

      // 创建一个可以安全序列化的hexagram_data对象，包含所有计算后的数据
      const hexagramDataForDB = {
        name: currentHexagram.name,
        number: currentHexagram.number,
        lines: currentHexagram.lines, // lines已经是YaoType[]字符串数组，不需要.map(line => line.type)
        sixSpirits: currentHexagram.sixSpirits,
        sixRelations: currentHexagram.sixRelations,
        worldPosition: currentHexagram.worldPosition,
        responsePosition: currentHexagram.responsePosition,
        hexagramType: currentHexagram.hexagramType,
        hiddenSpirits: currentHexagram.hiddenSpirits,
        divinationInfo: currentHexagram.divinationInfo,
        changedHexagram: currentHexagram.changedHexagram ? {
          name: currentHexagram.changedHexagram.name,
          lines: currentHexagram.changedHexagram.lines,
          sixSpirits: currentHexagram.sixSpirits, // 变卦使用与本卦相同的六神
          sixRelations: currentHexagram.changedHexagram.sixRelations,
          worldPosition: currentHexagram.changedHexagram.worldPosition,
          responsePosition: currentHexagram.changedHexagram.responsePosition,
        } : undefined
      };

      console.log('🔍 准备保存的hexagramDataForDB:', hexagramDataForDB);

      // 从hexagram_data中生成显示用的卦象文本，确保与生成的数据一致
      const originalHexagramText = hexagramDataForDB.lines.map(lineType => {
        switch (lineType) {
          case 'yang': return '━━━';
          case 'yin': return '━ ━';
          case 'yang_changing': return '━━━○';
          case 'yin_changing': return '━ ━●';
          default: return '━ ━';
        }
      }).join('\n');

      const changedHexagramText = hexagramDataForDB.changedHexagram?.lines ?
        hexagramDataForDB.changedHexagram.lines.map(lineType => {
          switch (lineType) {
            case 'yang': return '━━━';
            case 'yin': return '━ ━';
            default: return '━ ━';
          }
        }).join('\n') : undefined;

      const recordData = {
        hexagram_name: hexagramDataForDB.name,
        hexagram_number: hexagramDataForDB.number,
        original_hexagram: originalHexagramText,
        changed_hexagram: changedHexagramText,
        coin_results: coinResults.map(result => result.sum),
        divination_time: divinationTime?.toISOString() || new Date().toISOString(),
        question: question.trim(),
        analysis_status: 'pending' as const,
        analysis_type: 'detailed' as const, // 设置为详细分析
        hexagram_data: hexagramDataForDB, // 保存完整的卦象数据，供DeepSeek分析使用
      };

      console.log('💾 分析请求已提交...');

      toast({
        title: t('analysisSubmitted'),
        description: t('analysisInProgress'),
      });

      // 立即跳转到profile页面
      console.log('🔄 准备跳转到profile页面...');
      navigate('/profile');
      console.log('✅ 跳转命令已执行');

    } catch (error) {
      console.error('保存记录失败:', error);
      toast({
        title: "提交失败",
        description: error.message || "提交分析请求时出现错误，请稍后重试",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };



  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* 开始占卜之旅 - 新的布局 */}
      <div className="text-center space-y-8 py-12">
        <div className="space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400 bg-clip-text text-transparent animate-pulse">
            开始您的占卜之旅
          </h2>
          <p className="text-lg text-slate-300 max-w-2xl mx-auto leading-relaxed">
            描述您的问题或困惑，AI智能占卜系统将为您提供专业的分析与指引
          </p>
        </div>

        {/* 问题输入区 - 新设计 */}
        <div className="max-w-4xl mx-auto">
          <div className="relative group">
            {/* 装饰性边框效果 */}
            <div className="absolute -inset-1 bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-blue-500/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

            <Textarea
              placeholder={t('questionPlaceholder')}
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              className="relative min-h-32 text-lg p-6 bg-slate-900/40 backdrop-blur-xl border border-slate-700/50 rounded-2xl resize-none focus:border-purple-400/50 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300 text-slate-100 placeholder:text-slate-400"
              style={{
                background: 'linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%)',
                backdropFilter: 'blur(20px)',
                boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.05)'
              }}
            />
            {question.length > 0 && (
              <div className="absolute bottom-4 right-4 text-sm text-slate-400">
                {question.length} 字符
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮区 - 新设计 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center max-w-md mx-auto">
          <Button
            onClick={handleAutoGenerate}
            disabled={showAutoToss || showManualInput}
            className="relative w-full sm:w-auto px-8 py-4 text-lg font-semibold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-500 hover:via-pink-500 hover:to-blue-500 text-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-105 overflow-hidden group"
            style={{
              boxShadow: '0 8px 32px rgba(139, 92, 246, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)'
            }}
          >
            <span className="relative z-10">自动摇卦</span>
            <div className="absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </Button>
          <Button
            onClick={() => setShowManualInput(true)}
            disabled={showAutoToss || showManualInput || !question.trim()}
            className="relative w-full sm:w-auto px-6 py-4 text-lg border border-slate-600/50 text-slate-200 hover:bg-slate-800/50 backdrop-blur-sm rounded-xl transition-all duration-300 hover:border-slate-500/70 group overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(30, 41, 59, 0.4) 0%, rgba(51, 65, 85, 0.3) 100%)',
              boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.05)'
            }}
          >
            <span className="relative z-10">手动摇卦</span>
            <div className="absolute inset-0 bg-gradient-to-r from-slate-700/20 to-slate-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </Button>
        </div>
      </div>

      {/* 自动摇卦 */}
      {showAutoToss && (
        <DivinationCard title={t('autoTossLong')} glowing>
          <AutoCoinToss
            onComplete={handleAutoComplete}
            onCancel={() => setShowAutoToss(false)}
          />
        </DivinationCard>
      )}

      {/* 手动摇卦输入 */}
      {showManualInput && (
        <DivinationCard title={t('manualTossLong')} glowing>
          <ManualCoinInput
            onComplete={handleManualComplete}
            onCancel={() => setShowManualInput(false)}
          />
        </DivinationCard>
      )}

      {/* 起卦结果显示 */}
      {coinResults.length > 0 && currentHexagram && (
        <DivinationCard title={t('tossResult')}>
          <div className="space-y-4">
            {/* 日期和基本信息 */}
            <div className="grid md:grid-cols-2 gap-4 p-4 bg-background/30 rounded-lg">
              <div className="space-y-2">
                 <div className="text-sm">
                   <span className="text-muted-foreground">{t('gregorianDate')}</span>
                   <span className="text-foreground">{currentHexagram.divinationInfo.date}</span>
                 </div>
                 <div className="text-sm">
                   <span className="text-muted-foreground">{t('lunarDate')}</span>
                   <span className="text-foreground">{currentHexagram.divinationInfo.stems.year}</span>
                 </div>
                 <div className="text-sm">
                   <span className="text-muted-foreground">{t('solarTerm')}</span>
                   <span className="text-divination-primary">{currentHexagram.divinationInfo.solarTerm}</span>
                 </div>
              </div>
              <div className="space-y-2">
                 <div className="text-sm">
                   <span className="text-muted-foreground">{t('stems')}</span>
                   <span className="text-foreground">
                     {currentHexagram.divinationInfo.stems.year} {currentHexagram.divinationInfo.stems.month} {currentHexagram.divinationInfo.stems.day} {currentHexagram.divinationInfo.stems.hour}
                   </span>
                 </div>
                 <div className="text-sm">
                   <span className="text-muted-foreground">{t('voidness')}</span>
                   <span className="text-destructive">
                     {currentHexagram.divinationInfo.voidness.year.join('')} {currentHexagram.divinationInfo.voidness.month.join('')} {currentHexagram.divinationInfo.voidness.day.join('')} {currentHexagram.divinationInfo.voidness.hour.join('')}
                   </span>
                 </div>
                 <div className="text-sm">
                   <span className="text-muted-foreground">{t('spirits')}</span>
                   <span className="text-divination-accent">{currentHexagram.divinationInfo.spirits.join(' ')}</span>
                 </div>
              </div>
            </div>

            {/* 摇卦过程 */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-muted-foreground">{t('tossProcess')}</h4>
              <div className="grid grid-cols-6 gap-4 text-center">
                {coinResults.map((result, index) => (
                  <div key={index} className="space-y-2 p-2 bg-background/20 rounded">
                    <div className="text-sm text-muted-foreground">{t('lineNumber').replace('{number}', String(index + 1))}</div>
                    <div className="space-y-1">
                      {result.coins.map((coin, coinIndex) => (
                        <div key={coinIndex} className="text-xs">
                          {coin === 3 ? t('heads') : t('tails')}
                        </div>
                      ))}
                    </div>
                    <div className="text-xs text-divination-primary font-medium">
                       {result.sum === 6 ? t('oldYin') :
                         result.sum === 9 ? t('oldYang') :
                         result.sum === 7 ? t('youngYang') : t('youngYin')}
                    </div>
                  </div>
                ))}
              </div>
              <div className="text-center text-sm text-muted-foreground">
                 {t('tossResultSummary')}{coinResults.map((result, index) => 
                   `${result.coins.filter(c => c === 3).length}${t('heads')}${result.coins.filter(c => c === 2).length}${t('tails')}`
                 ).join(' ')}
              </div>
            </div>
          </div>
        </DivinationCard>
      )}

      {/* 卦象显示区 */}
      {currentHexagram && (
        <HexagramDisplay
          hexagram={currentHexagram}
        />
      )}

      {/* 分析按钮区 */}
      {currentHexagram && (
        <DivinationCard title={t('aiAnalysisLong')}>
          <div className="space-y-4">
            <div className="flex gap-4">
              <Button
                onClick={handleAnalyze}
                disabled={isAnalyzing || !user}
                variant="golden"
                className="flex-1"
              >
                {t('detailedAnalysisLong')}
                <Badge variant="destructive" className="ml-2">{t('paid')}</Badge>
              </Button>
            </div>

            {user && (
              <Button
                onClick={() => window.open('/profile', '_blank')}
                variant="ghost"
                className="flex-1"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                {t('viewHistory')}
              </Button>
            )}
            
            {isAnalyzing && (
              <div className="text-center py-8">
                <div className="animate-spin w-8 h-8 border-4 border-divination-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-muted-foreground">{t('aiAnalyzing')}</p>
              </div>
            )}
            
            {analysisResult && (
              <div className="mt-6 p-4 bg-background/30 rounded-lg">
                <MarkdownRenderer
                  content={analysisResult}
                  className="text-sm leading-relaxed"
                />
              </div>
            )}
          </div>
        </DivinationCard>
      )}
    </div>
  );
};