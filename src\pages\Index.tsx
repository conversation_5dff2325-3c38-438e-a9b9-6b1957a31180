import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { DivinationInterface } from '@/components/DivinationInterface';
import { DivinationCard } from '@/components/DivinationCard';

import { ThemeSwitcher } from '@/components/ThemeSwitcher';
import { SavedRecordsPanel } from '@/components/SavedRecordsPanel';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Zap, Shield, Users, CheckCircle, XCircle, Sparkles, Heart, Scroll, BarChart3, Database, Cpu, Star, Sun, Flower } from 'lucide-react';
import { toast } from 'sonner';
import { useAuthStore } from '@/stores/authStore';
import { useThemeStore } from '@/stores/themeStore';
import { useTranslation } from '@/hooks/useTranslation';
import { supabase } from '@/lib/supabase';
import modernBg from '@/assets/modern-tech-bg.jpg';
import cuteBg from '@/assets/cute-warm-bg.jpg';
import classicBg from '@/assets/classic-chinese-bg.jpg';
// import heroBackground from '@/assets/hero-background.jpg';

const Index = () => {
  console.log('Index page rendering...')
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useAuthStore();
  const { currentTheme } = useThemeStore();
  const { t } = useTranslation();

  // 获取主题特色图标
  const getThemeIcon = () => {
    switch (currentTheme) {
      case 'modern': return Zap;
      case 'cute': return Heart;
      case 'classic': return Scroll;
      default: return Zap;
    }
  };

  const ThemeIcon = getThemeIcon();

  // 获取主题背景图
  const getThemeBackground = () => {
    switch (currentTheme) {
      case 'modern': return modernBg;
      case 'cute': return cuteBg;
      case 'classic': return classicBg;
      default: return modernBg;
    }
  };

  // 获取主题标题
  const getThemeTitle = () => {
    switch (currentTheme) {
      case 'modern': return t('modernTitle');
      case 'cute': return t('cuteTitle');
      case 'classic': return t('classicTitle');
      default: return t('classicTitle');
    }
  };

  // 获取主题描述
  const getThemeDescription = () => {
    switch (currentTheme) {
      case 'modern': return t('modernDesc');
      case 'cute': return t('cuteDesc');
      case 'classic': return t('classicDesc');
      default: return t('classicDesc');
    }
  };

  useEffect(() => {
    const handleAuthCallback = async () => {
      // 检查认证回调参数
      const authSuccess = searchParams.get('auth_success');
      const authError = searchParams.get('auth_error');

      if (authSuccess === 'true') {
        console.log('检测到认证成功回调');

        toast.success(t('loginSuccessWelcome'), {
          duration: 3000,
          icon: <CheckCircle className="h-4 w-4" />,
        });

        // 清理URL参数
        setSearchParams({}, { replace: true });
      } else if (authError) {
        console.log('检测到认证错误回调:', authError);
        toast.error(`${t('loginFailedIndex')}: ${decodeURIComponent(authError)}`, {
          duration: 5000,
          icon: <XCircle className="h-4 w-4" />,
        });

        // 清理URL参数
        setSearchParams({}, { replace: true });
      }
    };

    handleAuthCallback();
  }, [searchParams, setSearchParams]);

  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section - 现代科技风 */}
      {currentTheme === 'modern' && (
        <section
          className="py-32 px-6 relative min-h-screen flex items-center overflow-hidden"
          style={{
            background: `
              radial-gradient(ellipse at top, rgba(29, 78, 216, 0.15) 0%, transparent 50%),
              radial-gradient(ellipse at bottom left, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
              radial-gradient(ellipse at bottom right, rgba(219, 39, 119, 0.1) 0%, transparent 50%),
              radial-gradient(ellipse at center right, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              linear-gradient(180deg, #0a0a0f 0%, #1a1a2e 30%, #16213e 70%, #0f172a 100%)
            `,
          }}
        >
          {/* 深度星空效果 */}
          <div className="absolute inset-0 overflow-hidden">
            {/* 大星星 */}
            {[...Array(20)].map((_, i) => (
              <div
                key={`big-star-${i}`}
                className="absolute bg-white rounded-full opacity-80"
                style={{
                  width: `${2 + Math.random() * 3}px`,
                  height: `${2 + Math.random() * 3}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `twinkle ${3 + Math.random() * 4}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 5}s`,
                  boxShadow: '0 0 6px rgba(255, 255, 255, 0.8)'
                }}
              />
            ))}
            {/* 中等星星 */}
            {[...Array(40)].map((_, i) => (
              <div
                key={`med-star-${i}`}
                className="absolute bg-blue-100 rounded-full opacity-60"
                style={{
                  width: `${1 + Math.random() * 2}px`,
                  height: `${1 + Math.random() * 2}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `twinkle ${2 + Math.random() * 3}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 4}s`,
                  boxShadow: '0 0 4px rgba(191, 219, 254, 0.6)'
                }}
              />
            ))}
            {/* 小星星 */}
            {[...Array(80)].map((_, i) => (
              <div
                key={`small-star-${i}`}
                className="absolute bg-purple-200 rounded-full opacity-40"
                style={{
                  width: `${0.5 + Math.random() * 1}px`,
                  height: `${0.5 + Math.random() * 1}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `twinkle ${1.5 + Math.random() * 2}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 3}s`
                }}
              />
            ))}
            {/* 流星效果 */}
            {[...Array(3)].map((_, i) => (
              <div
                key={`meteor-${i}`}
                className="absolute w-1 h-1 bg-white rounded-full opacity-0"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 50}%`,
                  animation: `meteor ${8 + Math.random() * 4}s linear infinite`,
                  animationDelay: `${Math.random() * 10}s`,
                  boxShadow: '0 0 8px rgba(255, 255, 255, 0.8), 0 0 16px rgba(59, 130, 246, 0.4)'
                }}
              />
            ))}
          </div>

          <div className="max-w-6xl mx-auto text-center space-y-16 relative z-10">
            <div className="space-y-8">
              <div className="flex justify-center mb-8">
                <Badge variant="secondary" className="text-sm px-6 py-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-300 border border-yellow-500/30 backdrop-blur-sm">
                  <Sparkles className="w-4 h-4 mr-2" />
                  {t('aiTechnology')}
                </Badge>
              </div>

              <h1 className="text-6xl md:text-7xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent leading-tight" style={{textShadow: '2px 2px 4px rgba(0,0,0,0.5)'}}>
                {getThemeTitle()}
              </h1>

              <p className="text-xl md:text-2xl text-gray-300 leading-relaxed max-w-4xl mx-auto" style={{textShadow: '1px 1px 2px rgba(0,0,0,0.5)'}}>
                {getThemeDescription()}
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <div className="bg-white/5 backdrop-blur-md rounded-2xl p-8 border border-white/10 hover:bg-white/10 transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-500/30 to-orange-500/30 rounded-full flex items-center justify-center mr-4">
                    <BarChart3 className="w-6 h-6 text-yellow-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">{t('intelligentAlgorithm')}</h3>
                </div>
                <p className="text-gray-300">{t('deepLearningAnalysis')}</p>
              </div>

              <div className="bg-white/5 backdrop-blur-md rounded-2xl p-8 border border-white/10 hover:bg-white/10 transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500/30 to-purple-500/30 rounded-full flex items-center justify-center mr-4">
                    <Database className="w-6 h-6 text-blue-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">{t('traditionalIntegration')}</h3>
                </div>
                <p className="text-gray-300">{t('modernTechTradition')}</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="golden"
                size="lg"
                className="text-lg px-12 py-6 rounded-xl bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-400 hover:to-orange-400 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                onClick={() => document.getElementById('divination-interface')?.scrollIntoView({ behavior: 'smooth' })}
              >
                <Zap className="w-5 h-5 mr-2" />
                {t('startAiAnalysis')}
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 rounded-xl border-white/20 text-white hover:bg-white/10 backdrop-blur-sm transition-all duration-300"
                onClick={() => document.getElementById('features-section')?.scrollIntoView({ behavior: 'smooth' })}
              >
                {t('learnMore')}
              </Button>
            </div>

          </div>
        </section>
      )}

      {/* Hero Section - 可爱暖色风 */}
      {currentTheme === 'cute' && (
        <section
          className="py-32 px-6 relative min-h-screen flex items-center hero-enhanced"
          style={{
            backgroundImage: `linear-gradient(rgba(255,182,193,0.85), rgba(255,218,185,0.75)), url(${getThemeBackground()})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundAttachment: 'fixed'
          }}
        >
          <div className="max-w-6xl mx-auto text-center space-y-16 w-full">
            <div className="space-y-10">
              <div className="flex justify-center space-x-6 mb-10">
                <Star className="w-12 h-12 text-yellow-400 animate-pulse" />
                <Heart className="w-16 h-16 text-pink-400 animate-bounce" />
                <Star className="w-12 h-12 text-yellow-400 animate-pulse" />
              </div>

              <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold gradient-text leading-tight" style={{textShadow: '3px 3px 6px rgba(0,0,0,0.15)'}}>
                {getThemeTitle()}
              </h1>

              <div className="bg-white/85 backdrop-blur-md rounded-3xl p-10 md:p-12 shadow-2xl border-4 border-pink-200 max-w-4xl mx-auto">
                <p className="text-xl md:text-2xl lg:text-3xl text-pink-700 leading-relaxed font-medium">
                  {getThemeDescription()}
                </p>
              </div>
            </div>

            <div className="flex flex-wrap justify-center gap-8">
              <div className="bg-white/90 backdrop-blur-sm rounded-full px-10 py-6 shadow-xl border-3 border-pink-200 hover:scale-105 transition-transform duration-300">
                <div className="flex items-center space-x-4">
                  <Heart className="w-8 h-8 text-pink-400" />
                  <span className="font-semibold text-lg text-pink-600">{t('warmCompanion')}</span>
                </div>
              </div>
              <div className="bg-white/90 backdrop-blur-sm rounded-full px-10 py-6 shadow-xl border-3 border-orange-200 hover:scale-105 transition-transform duration-300">
                <div className="flex items-center space-x-4">
                  <Sun className="w-8 h-8 text-orange-400" />
                  <span className="font-semibold text-lg text-orange-600">{t('sunnyHealing')}</span>
                </div>
              </div>
              <div className="bg-white/90 backdrop-blur-sm rounded-full px-10 py-6 shadow-xl border-3 border-yellow-200 hover:scale-105 transition-transform duration-300">
                <div className="flex items-center space-x-4">
                  <Sparkles className="w-8 h-8 text-yellow-400" />
                  <span className="font-semibold text-lg text-yellow-600">{t('magicalDivination')}</span>
                </div>
              </div>
            </div>

            <Button
              variant="golden"
              size="lg"
              className="text-2xl px-20 py-8 rounded-full shadow-2xl hover:scale-110 transition-all duration-300 border-4 border-pink-300"
              onClick={() => document.getElementById('divination-interface')?.scrollIntoView({ behavior: 'smooth' })}
            >
              <Heart className="w-8 h-8 mr-4" />
              {t('startDivination')}
            </Button>
          </div>
        </section>
      )}

      {/* Hero Section - 经典国学风 */}
      {currentTheme === 'classic' && (
        <section 
          className="py-32 px-6 relative"
          style={{
            backgroundImage: `linear-gradient(rgba(139,69,19,0.8), rgba(160,82,45,0.6)), url(${getThemeBackground()})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }}
        >
          <div className="max-w-5xl mx-auto text-center space-y-16">
            <div className="space-y-8">
              <div className="flex justify-center mb-8">
                <div className="w-32 h-32 bg-gradient-to-br from-amber-600 to-orange-700 rounded-full flex items-center justify-center border-4 border-amber-400 shadow-2xl">
                  <Scroll className="w-16 h-16 text-white" />
                </div>
              </div>
              
              <h1 className="text-6xl md:text-7xl font-bold gradient-text leading-tight" style={{fontFamily: 'serif', textShadow: '3px 3px 6px rgba(0,0,0,0.3)'}}>
                {getThemeTitle()}
              </h1>
              
              <div className="bg-amber-50/90 backdrop-blur-sm rounded-lg p-8 border-2 border-amber-600 shadow-xl max-w-3xl mx-auto">
                <p className="text-xl md:text-2xl text-amber-900 leading-relaxed" style={{fontFamily: 'serif'}}>
                  {getThemeDescription()}
                </p>
              </div>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-amber-50/80 backdrop-blur-sm rounded-lg p-6 border-2 border-amber-600 shadow-lg">
                <Scroll className="w-12 h-12 text-amber-700 mx-auto mb-4" />
                <h3 className="text-lg font-bold text-amber-900 mb-2">{t('traditionalCulture')}</h3>
                <p className="text-amber-800 text-sm">{t('inheritWisdom')}</p>
              </div>
              <div className="bg-amber-50/80 backdrop-blur-sm rounded-lg p-6 border-2 border-amber-600 shadow-lg">
                <Shield className="w-12 h-12 text-amber-700 mx-auto mb-4" />
                <h3 className="text-lg font-bold text-amber-900 mb-2">{t('authorityInterpretation')}</h3>
                <p className="text-amber-800 text-sm">{t('professionalAnalysis')}</p>
              </div>
              <div className="bg-amber-50/80 backdrop-blur-sm rounded-lg p-6 border-2 border-amber-600 shadow-lg">
                <Users className="w-12 h-12 text-amber-700 mx-auto mb-4" />
                <h3 className="text-lg font-bold text-amber-900 mb-2">{t('deepGuidance')}</h3>
                <p className="text-amber-800 text-sm">{t('lifeWisdomDesc')}</p>
              </div>
            </div>
            
            <Button 
              variant="golden" 
              size="lg" 
              className="text-xl px-16 py-6 rounded-lg border-2 border-amber-600 shadow-xl hover:shadow-2xl transition-all duration-300"
              onClick={() => document.getElementById('divination-interface')?.scrollIntoView({ behavior: 'smooth' })}
            >
              <Scroll className="w-6 h-6 mr-3" />
              {t('startCoinToss')}
            </Button>
          </div>
        </section>
      )}

      {/* Main Interface */}
      <div
        id="divination-interface"
        className="py-16 px-6 relative"
        style={{
          background: currentTheme === 'modern' ? `
            radial-gradient(ellipse at top, rgba(29, 78, 216, 0.08) 0%, transparent 50%),
            radial-gradient(ellipse at bottom, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
            linear-gradient(180deg, #0f172a 0%, #1e293b 50%, #0f172a 100%)
          ` : undefined
        }}
      >
        {/* 延续星空效果 */}
        {currentTheme === 'modern' && (
          <div className="absolute inset-0 overflow-hidden opacity-30">
            {[...Array(30)].map((_, i) => (
              <div
                key={`interface-star-${i}`}
                className="absolute bg-white rounded-full opacity-40"
                style={{
                  width: `${0.5 + Math.random() * 1.5}px`,
                  height: `${0.5 + Math.random() * 1.5}px`,
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  animation: `twinkle ${2 + Math.random() * 3}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 4}s`
                }}
              />
            ))}
          </div>
        )}
        <div className="max-w-6xl mx-auto relative z-10">
          <div className="max-w-4xl mx-auto">
            <DivinationInterface />
          </div>
        </div>
      </div>

      {/* Features Section */}
      <section id="features-section" className="py-16 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className={`text-3xl font-bold mb-4 ${
              currentTheme === 'cute' ? 'text-pink-600' : ''
            }`}>{t('coreFeaturesHighlight')}</h2>
            <p className={`text-lg ${
              currentTheme === 'cute' ? 'text-pink-500' : 'text-muted-foreground'
            }`}>
              {t('professionalAnalysisDesc')}
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <DivinationCard title={currentTheme === 'modern' ? t('intelligentPlanning') : 
                                   currentTheme === 'cute' ? t('easyPlanning') : 
                                   t('traditionalPlanning')}>
              <div className="space-y-4">
                <div className="text-center py-4">
                   <div className={cn(
                     "w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center",
                     currentTheme === 'cute' ? 
                       "bg-gradient-to-br from-primary/30 to-secondary/30" :
                       "bg-gradient-to-br from-divination-primary to-divination-accent"
                   )}>
                     {currentTheme === 'modern' ? <TrendingUp className="w-8 h-8 text-white" /> :
                      currentTheme === 'cute' ? <Heart className="w-8 h-8 text-primary" /> :
                      <Scroll className="w-8 h-8 text-white" />}
                   </div>
                 </div>
                  <p className={cn(
                    "text-sm text-center mb-4",
                    currentTheme === 'cute' ? 'text-cardForeground' : 'text-muted-foreground'
                  )}>
                    {currentTheme === 'modern' ? t('traditionalDivinationDesc') :
                     currentTheme === 'cute' ? t('cuteDivinationDesc') :
                     t('classicDivinationDesc')}
                  </p>
                 <ul className={cn(
                   "text-sm space-y-1",
                   currentTheme === 'cute' && 'text-cardForeground'
                 )}>
                   <li className="flex items-center">
                     <span className={cn(
                       "w-2 h-2 rounded-full mr-2",
                       currentTheme === 'cute' ? 'bg-primary' : 'bg-divination-secondary'
                     )}></span>
                      {currentTheme === 'modern' ? t('intelligentPlanning') : 
                       currentTheme === 'cute' ? t('easyPlanning') : 
                       t('traditionalPlanning')}
                   </li>
                   <li className="flex items-center">
                     <span className={cn(
                       "w-2 h-2 rounded-full mr-2",
                       currentTheme === 'cute' ? 'bg-primary' : 'bg-divination-secondary'
                     )}></span>
                     {t('yinMoving')}/{t('yangMoving')}
                   </li>
                   <li className="flex items-center">
                     <span className={cn(
                       "w-2 h-2 rounded-full mr-2",
                       currentTheme === 'cute' ? 'bg-primary' : 'bg-divination-secondary'
                     )}></span>
                     {t('world')}/{t('response')}
                   </li>
                </ul>
              </div>
            </DivinationCard>

            <DivinationCard title={currentTheme === 'modern' ? t('aiSmartAnalysis') : 
                                   currentTheme === 'cute' ? t('warmInterpretation') : 
                                   t('professionalAnalysisTitle')} glowing>
              <div className="space-y-4">
                <div className="text-center py-4">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-divination-secondary to-divination-warning flex items-center justify-center">
                    {currentTheme === 'modern' ? <Zap className="w-8 h-8 text-divination-accent" /> :
                     currentTheme === 'cute' ? <Sparkles className="w-8 h-8 text-divination-accent" /> :
                     <Shield className="w-8 h-8 text-divination-accent" />}
                  </div>
                </div>
                <p className="text-sm text-muted-foreground text-center">
                  {currentTheme === 'modern' ? t('modernAnalysisDesc') :
                   currentTheme === 'cute' ? t('cuteAnalysisDesc') :
                   t('classicAnalysisDesc')}
                </p>
                <ul className="text-sm space-y-1">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-divination-secondary rounded-full mr-2"></span>
                    {t('professionalDetailedReading')}
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-divination-secondary rounded-full mr-2"></span>
                    {t('personalizedSuggestions')}
                  </li>
                </ul>
              </div>
            </DivinationCard>

            <DivinationCard title={currentTheme === 'modern' ? t('cloudManagement') : 
                                   currentTheme === 'cute' ? t('warmRecordsTitle') : 
                                   t('archiveManagement')}>
              <div className="space-y-4">
                <div className="text-center py-4">
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-divination-accent to-divination-primary flex items-center justify-center">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                </div>
                <p className="text-sm text-muted-foreground text-center">
                  {currentTheme === 'modern' ? t('modernRecordDesc') :
                   currentTheme === 'cute' ? t('cuteRecordDesc') :
                   t('classicRecordDesc')}
                </p>
                <ul className="text-sm space-y-1">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-divination-secondary rounded-full mr-2"></span>
                     {currentTheme === 'modern' ? t('cloudHistoryRecords') : 
                      currentTheme === 'cute' ? t('warmRecordSaving') : 
                      t('completeHistoryArchive')}
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-divination-secondary rounded-full mr-2"></span>
                     {currentTheme === 'modern' ? t('intelligentClassification') : 
                      currentTheme === 'cute' ? t('shareCollection') : 
                      t('shareCollection')}
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-divination-secondary rounded-full mr-2"></span>
                    {t('oneKeySharing')}
                  </li>
                </ul>
              </div>
            </DivinationCard>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default Index;
