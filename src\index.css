@tailwind base;
@tailwind components;
@tailwind utilities;

/* 六爻排盘网站设计系统 - 传统与现代融合 */
/* Six Lines Divination Website Design System - Traditional meets Modern */

@layer base {
  :root {
    /* 默认现代科技风主题 */
    --background: 240 10% 4%;
    --foreground: 210 40% 95%;
    --card: 240 8% 8%;
    --card-foreground: 210 40% 95%;
    --popover: 240 8% 8%;
    --popover-foreground: 210 40% 95%;
    --primary: 230 40% 20%;
    --primary-foreground: 210 40% 98%;
    --secondary: 45 90% 55%;
    --secondary-foreground: 230 40% 15%;
    --muted: 240 8% 15%;
    --muted-foreground: 215 20% 65%;
    --accent: 270 50% 25%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 70% 55%;
    --destructive-foreground: 210 40% 98%;
    --border: 240 15% 18%;
    --input: 240 15% 18%;
    --ring: 230 40% 20%;
    --radius: 0.5rem;

    /* 六爻专用色彩 */
    --divination-primary: 230 60% 35%;
    --divination-secondary: 45 95% 60%;
    --divination-accent: 270 55% 30%;
    --divination-success: 142 70% 45%;
    --divination-warning: 38 92% 50%;

    /* 渐变色彩 */
    --gradient-primary: linear-gradient(135deg, hsl(230, 60%, 35%), hsl(270, 55%, 30%));
    --gradient-secondary: linear-gradient(135deg, hsl(45, 95%, 60%), hsl(38, 92%, 50%));
    --gradient-background: linear-gradient(180deg, hsl(240, 8%, 8%), hsl(240, 10%, 4%));
    --gradient-card: linear-gradient(145deg, hsl(240, 8%, 8%), hsl(240, 10%, 6%));

    /* 阴影效果 */
    --shadow-mystical: 0 10px 30px -5px hsl(230 60% 35% / 0.3);
    --shadow-glow: 0 0 40px hsl(45 95% 60% / 0.2);
    --shadow-card: 0 8px 25px -5px hsl(240 10% 4% / 0.4);

    /* 动画时间 */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Sidebar 变量 */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* 可爱暖色风主题 */
  .theme-cute {
    --background: 20 100% 98%;
    --foreground: 20 10% 15%;
    --card: 15 100% 95%;
    --card-foreground: 20 10% 15%;
    --primary: 340 75% 55%;
    --primary-foreground: 0 0% 100%;
    --secondary: 35 95% 65%;
    --secondary-foreground: 20 10% 15%;
    --muted: 20 50% 90%;
    --muted-foreground: 20 10% 40%;
    --accent: 280 60% 70%;
    --accent-foreground: 0 0% 100%;
    --border: 20 30% 85%;
    --divination-primary: 340 75% 55%;
    --divination-secondary: 35 95% 65%;
    --divination-accent: 280 60% 70%;
    --gradient-primary: linear-gradient(135deg, hsl(340, 75%, 55%), hsl(280, 60%, 70%));
    --gradient-secondary: linear-gradient(135deg, hsl(35, 95%, 65%), hsl(45, 100%, 70%));
    --gradient-background: linear-gradient(180deg, hsl(15, 100%, 95%), hsl(20, 100%, 98%));
    --shadow-mystical: 0 10px 30px -5px hsl(340 75% 55% / 0.3);
    --shadow-glow: 0 0 40px hsl(35 95% 65% / 0.3);
  }

  /* 经典国学风主题 */
  .theme-classic {
    --background: 50 20% 95%;
    --foreground: 30 10% 10%;
    --card: 45 30% 92%;
    --card-foreground: 30 10% 10%;
    --primary: 15 75% 28%;
    --primary-foreground: 50 20% 95%;
    --secondary: 45 85% 35%;
    --secondary-foreground: 50 20% 95%;
    --muted: 45 20% 85%;
    --muted-foreground: 30 10% 40%;
    --accent: 25 60% 45%;
    --accent-foreground: 50 20% 95%;
    --border: 45 30% 80%;
    --divination-primary: 15 75% 28%;
    --divination-secondary: 45 85% 35%;
    --divination-accent: 25 60% 45%;
    --gradient-primary: linear-gradient(135deg, hsl(15, 75%, 28%), hsl(25, 60%, 45%));
    --gradient-secondary: linear-gradient(135deg, hsl(45, 85%, 35%), hsl(55, 80%, 40%));
    --gradient-background: linear-gradient(180deg, hsl(45, 30%, 92%), hsl(50, 20%, 95%));
    --shadow-mystical: 0 10px 30px -5px hsl(15 75% 28% / 0.3);
    --shadow-glow: 0 0 40px hsl(45 85% 35% / 0.3);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    background: var(--gradient-background);
    background-attachment: fixed;
    min-height: 100vh;
    transition: var(--transition-smooth);
  }
}

@layer components {
  /* 六爻卡片样式 */
  .divination-card {
    @apply rounded-lg border border-border bg-card text-card-foreground shadow-lg;
    background: var(--gradient-card);
    box-shadow: var(--shadow-card);
    transition: var(--transition-smooth);
  }

  .divination-card:hover {
    box-shadow: var(--shadow-mystical);
    transform: translateY(-2px);
  }

  /* 神秘按钮样式 */
  .mystical-button {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300;
    background: var(--gradient-primary);
    color: hsl(var(--primary-foreground));
    box-shadow: var(--shadow-mystical);
  }

  .mystical-button:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  /* 星空动画效果 */
  @keyframes twinkle {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
  }

  @keyframes meteor {
    0% {
      opacity: 0;
      transform: translateX(-100px) translateY(-100px) rotate(45deg);
    }
    10% {
      opacity: 1;
    }
    90% {
      opacity: 1;
    }
    100% {
      opacity: 0;
      transform: translateX(300px) translateY(300px) rotate(45deg);
    }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.3), 0 0 40px rgba(139, 92, 246, 0.1);
    }
    50% {
      box-shadow: 0 0 30px rgba(139, 92, 246, 0.5), 0 0 60px rgba(139, 92, 246, 0.2);
    }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  /* 金色按钮样式 */
  .golden-button {
    @apply px-6 py-3 rounded-lg font-medium transition-all duration-300;
    background: var(--gradient-secondary);
    color: hsl(var(--secondary-foreground));
    box-shadow: 0 4px 15px hsl(45 95% 60% / 0.3);
  }

  .golden-button:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-1px);
  }

  /* 六爻爻位样式 */
  .yao-line {
    @apply w-full h-2 rounded-full transition-all duration-500;
    background: var(--gradient-primary);
    box-shadow: 0 2px 8px hsl(230 60% 35% / 0.4);
  }

  .yao-line.changing {
    background: var(--gradient-secondary);
    box-shadow: var(--shadow-glow);
    animation: pulse 1.5s infinite;
  }

  /* 文字渐变效果 */
  .gradient-text {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 发光边框效果 */
  .glow-border {
    @apply border border-border rounded-lg;
    box-shadow: 0 0 20px hsl(230 60% 35% / 0.2);
    transition: var(--transition-smooth);
  }

  .glow-border:hover {
    box-shadow: 0 0 30px hsl(45 95% 60% / 0.3);
  }

  /* 主题特色增强 */
  .theme-modern .divination-card {
    backdrop-filter: blur(10px);
    border: 1px solid hsl(var(--border) / 0.8);
  }

  .theme-cute .divination-card {
    border-radius: 1rem;
    box-shadow: 0 8px 32px hsl(340 75% 55% / 0.15);
  }

  .theme-classic .divination-card {
    border: 2px solid hsl(var(--border));
    background: linear-gradient(145deg, hsl(var(--card)), hsl(var(--muted) / 0.3));
  }

  /* 按钮主题变体 */
  .theme-cute .golden-button {
    border-radius: 2rem;
    box-shadow: 0 6px 20px hsl(340 75% 55% / 0.3);
  }

  .theme-classic .golden-button {
    border: 1px solid hsl(var(--secondary));
    box-shadow: 0 4px 12px hsl(15 75% 28% / 0.4);
  }

  /* 可爱风格特殊边框 */
  .theme-cute .border-3 {
    border-width: 3px;
  }

  /* 可爱风格Hero区域增强效果 */
  .theme-cute .hero-enhanced {
    background-attachment: fixed;
  }

  .theme-cute .hero-enhanced .gradient-text {
    background: linear-gradient(135deg, hsl(340, 75%, 55%), hsl(280, 60%, 70%), hsl(35, 95%, 65%));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes mystical-glow {
  0%, 100% {
    box-shadow: 0 0 20px hsl(230 60% 35% / 0.2);
  }
  50% {
    box-shadow: 0 0 40px hsl(45 95% 60% / 0.4);
  }
}

/* 主题特色动画 */
.theme-cute * {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.theme-modern * {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.theme-classic * {
  transition: all 0.4s ease-in-out;
}